import React, { useState } from 'react';
import {
  ScrollView,
  View,
  Text,
  Modal,
  Pressable,
  Dimensions,
} from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';
import {
  M3EButton,
  M3EButtonGroups,
  M3EFAB,
  M3EIconButton,
} from '@/components/ui/m3e-button';
import { M3ECheckbox } from '@/components/ui/m3e-checkbox';
import { M3EChipGroup } from '@/components/ui/m3e-chips';
import { M3ETimePicker } from '@/components/ui/m3e-time-picker';
import { M3ESlider } from '@/components/ui/m3e-slider';
import { M3ESnackbar } from '@/components/ui/m3e-snackbar';
import { M3ESwitch } from '@/components/ui/m3e-switch';
import { M3ETabs } from '@/components/ui/m3e-tabs';
import { M3ETextField } from '@/components/ui/m3e-text-field';
import { M3EToolbar } from '@/components/ui/m3e-toolbar';
import { M3ETooltip } from '@/components/ui/m3e-tooltip';
import { M3EDatePicker } from '@/components/ui/m3e-date-picker';
import { Ionicons } from '@expo/vector-icons';

interface M3EDemoModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function M3EDemoModal({ visible, onClose }: M3EDemoModalProps) {
  const theme = useAppTheme();
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(0);
  const [checkboxStates, setCheckboxStates] = useState({
    basic: false,
    indeterminate: true,
    error: false,
  });
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedChips, setSelectedChips] = useState([0]);
  const [sliderValue, setSliderValue] = useState(50);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [switchValue, setSwitchValue] = useState(false);
  const [activeTab, setActiveTab] = useState('tab1');
  const [textFieldValue, setTextFieldValue] = useState('');
  const [showTooltip, setShowTooltip] = useState(false);

  const isDark = theme.dark;
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  // 响应式尺寸计算
  const isTablet = screenWidth >= 768;
  const isLargeScreen = screenWidth >= 1024;

  // 响应式样式类
  const getResponsiveClasses = () => ({
    modalOverlay: 'flex-1 justify-center items-center p-4 md:p-8',
    modalContainer: `w-full ${
      isLargeScreen
        ? 'max-w-[800px]'
        : isTablet
        ? 'max-w-[600px]'
        : 'max-w-[400px]'
    } ${
      isTablet ? 'h-[85%]' : 'h-[90%]'
    } bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg`,
    header: `flex-row justify-between items-center ${
      isTablet ? 'p-6' : 'p-4'
    } border-b border-outline-variant dark:border-outline-variant-dark`,
    headerTitle: `${
      isTablet ? 'text-2xl' : 'text-xl'
    } font-semibold text-on-surface dark:text-on-surface-dark`,
    closeButton:
      'p-2 rounded-full bg-surface-variant dark:bg-surface-variant-dark',
    scrollContent: isTablet ? 'p-6' : 'p-4',
    section: isTablet ? 'mb-10' : 'mb-8',
    sectionTitle: `${
      isTablet ? 'text-xl' : 'text-lg'
    } font-semibold text-on-surface dark:text-on-surface-dark ${
      isTablet ? 'mb-5' : 'mb-4'
    }`,
    subsectionTitle: `${
      isTablet ? 'text-lg' : 'text-base'
    } font-medium text-on-surface dark:text-on-surface-dark ${
      isTablet ? 'mb-4 mt-5' : 'mb-3 mt-4'
    }`,
    buttonRow: `flex-row flex-wrap ${isTablet ? 'gap-4 mb-5' : 'gap-3 mb-4'}`,
    fabContainer: `flex-row flex-wrap ${
      isTablet ? 'gap-5 mb-5' : 'gap-4 mb-4'
    }`,
    sliderContainer: `${isTablet ? 'mb-5' : 'mb-4'}`,
    sliderRow: `flex-row items-center ${isTablet ? 'mb-4' : 'mb-3'}`,
    sliderLabel: `${
      isTablet ? 'text-base' : 'text-sm'
    } font-medium text-on-surface dark:text-on-surface-dark ${
      isTablet ? 'w-12 mr-4' : 'w-10 mr-3'
    }`,
    verticalSliderContainer: `flex-row justify-center items-center ${
      isTablet ? 'mb-5' : 'mb-4'
    }`,
  });

  const classes = getResponsiveClasses();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View
        className={classes.modalOverlay}
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
      >
        <View className={classes.modalContainer}>
          {/* Header */}
          <View className={classes.header}>
            <Text className={classes.headerTitle}>M3E 组件演示</Text>
            <Pressable className={classes.closeButton} onPress={onClose}>
              <Ionicons
                name="close"
                size={20}
                color={isDark ? '#E6E0E9' : '#1D1B20'}
              />
            </Pressable>
          </View>

          {/* Content */}
          <ScrollView className={classes.scrollContent}>
            {/* M3E Buttons */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Buttons</Text>

              <Text className={classes.subsectionTitle}>Filled Buttons</Text>
              <View className={classes.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="small"
                  onPress={() => console.log('Small filled pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="medium"
                  onPress={() => console.log('Medium filled pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="large"
                  onPress={() => console.log('Large filled pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text className={classes.subsectionTitle}>Outlined Buttons</Text>
              <View className={classes.buttonRow}>
                <M3EButton
                  variant="outlined"
                  size="small"
                  onPress={() => console.log('Small outlined pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  onPress={() => console.log('Medium outlined pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="large"
                  onPress={() => console.log('Large outlined pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text className={classes.subsectionTitle}>Text Buttons</Text>
              <View className={classes.buttonRow}>
                <M3EButton
                  variant="text"
                  size="small"
                  onPress={() => console.log('Small text pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="medium"
                  onPress={() => console.log('Medium text pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="large"
                  onPress={() => console.log('Large text pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text className={classes.subsectionTitle}>With Icons</Text>
              <View className={classes.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="medium"
                  icon="add"
                  onPress={() => console.log('Icon button pressed')}
                >
                  Add Item
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  icon="download"
                  onPress={() => console.log('Download pressed')}
                >
                  Download
                </M3EButton>
              </View>
            </View>

            {/* M3E Button Groups */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Button Groups</Text>

              <Text className={classes.subsectionTitle}>
                Round Button Groups
              </Text>
              <View className={classes.buttonRow}>
                <M3EButtonGroups
                  type="round"
                  size="medium"
                  slots={3}
                  labels={['First', 'Second', 'Third']}
                  selectedIndex={selectedGroupIndex}
                  onPress={setSelectedGroupIndex}
                />
              </View>

              <Text className={classes.subsectionTitle}>
                Square Button Groups
              </Text>
              <View className={classes.buttonRow}>
                <M3EButtonGroups
                  type="square"
                  size="medium"
                  slots={4}
                  labels={['Option A', 'Option B', 'Option C', 'Option D']}
                  selectedIndex={0}
                  onPress={(index) =>
                    console.log('Square group pressed:', index)
                  }
                />
              </View>
            </View>

            {/* M3E FAB */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E FAB</Text>

              <Text className={classes.subsectionTitle}>Standard FABs</Text>
              <View className={classes.fabContainer}>
                <M3EFAB
                  size="small"
                  variant="primary"
                  icon={<Ionicons name="add" size={18} color="#FFFFFF" />}
                  onPress={() => console.log('Small FAB pressed')}
                />
                <M3EFAB
                  size="medium"
                  variant="primary"
                  icon={<Ionicons name="add" size={24} color="#FFFFFF" />}
                  onPress={() => console.log('Medium FAB pressed')}
                />
                <M3EFAB
                  size="large"
                  variant="primary"
                  icon={<Ionicons name="add" size={36} color="#FFFFFF" />}
                  onPress={() => console.log('Large FAB pressed')}
                />
              </View>
            </View>

            {/* M3E Icon Button */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Icon Button</Text>

              <Text className={classes.subsectionTitle}>
                Standard Icon Buttons
              </Text>
              <View className={classes.buttonRow}>
                <M3EIconButton
                  variant="standard"
                  size="small"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={20}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard small pressed')}
                />
                <M3EIconButton
                  variant="standard"
                  size="medium"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={24}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard medium pressed')}
                />
              </View>
            </View>

            {/* M3E Checkboxes */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Checkboxes</Text>

              <Text className={classes.subsectionTitle}>Basic Checkboxes</Text>
              <View className={classes.buttonRow}>
                <M3ECheckbox
                  checked={checkboxStates.basic}
                  onValueChange={(checked) =>
                    setCheckboxStates((prev) => ({ ...prev, basic: checked }))
                  }
                  label="基础复选框"
                />
                <M3ECheckbox
                  indeterminate={checkboxStates.indeterminate}
                  label="不确定状态"
                />
                <M3ECheckbox
                  checked={checkboxStates.error}
                  error={true}
                  onValueChange={(checked) =>
                    setCheckboxStates((prev) => ({ ...prev, error: checked }))
                  }
                  label="错误状态"
                />
              </View>
            </View>

            {/* M3E Chips */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Chips</Text>

              <Text className={classes.subsectionTitle}>Chip Group</Text>
              <View className={classes.buttonRow}>
                <M3EChipGroup
                  chips={[
                    { label: '选项 1', variant: 'filter' },
                    { label: '选项 2', variant: 'filter' },
                    { label: '选项 3', variant: 'filter' },
                  ]}
                  selectedIndices={selectedChips}
                  multiSelect={true}
                  onSelectionChange={(indices) =>
                    setSelectedChips(indices as number[])
                  }
                />
              </View>
            </View>

            {/* M3E Date & Time Pickers */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>
                M3E Date & Time Pickers
              </Text>

              <Text className={classes.subsectionTitle}>Date Picker</Text>
              <View className={classes.buttonRow}>
                <M3EDatePicker
                  value={selectedDate}
                  onDateChange={setSelectedDate}
                  label="选择日期"
                />
              </View>

              <Text className={classes.subsectionTitle}>Time Picker</Text>
              <View className={classes.buttonRow}>
                <M3ETimePicker
                  value={selectedTime}
                  onTimeChange={setSelectedTime}
                  label="选择时间"
                />
              </View>
            </View>

            {/* M3E Slider */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Slider</Text>

              <Text className={classes.subsectionTitle}>Size Variants</Text>
              <View className={classes.sliderContainer}>
                <View className={classes.sliderRow}>
                  <Text className={classes.sliderLabel}>XS:</Text>
                  <M3ESlider
                    value={sliderValue}
                    onValueChange={setSliderValue}
                    minimumValue={0}
                    maximumValue={100}
                    orientation="horizontal"
                    size="xs"
                    showValueIndicator={true}
                    className="flex-1"
                  />
                </View>
                <View className={classes.sliderRow}>
                  <Text className={classes.sliderLabel}>SM:</Text>
                  <M3ESlider
                    value={sliderValue}
                    onValueChange={setSliderValue}
                    minimumValue={0}
                    maximumValue={100}
                    orientation="horizontal"
                    size="sm"
                    showValueIndicator={true}
                    className="flex-1"
                  />
                </View>
                <View className={classes.sliderRow}>
                  <Text className={classes.sliderLabel}>MD:</Text>
                  <M3ESlider
                    value={sliderValue}
                    onValueChange={setSliderValue}
                    minimumValue={0}
                    maximumValue={100}
                    orientation="horizontal"
                    size="md"
                    showValueIndicator={true}
                    className="flex-1"
                  />
                </View>
                <View className={classes.sliderRow}>
                  <Text className={classes.sliderLabel}>LG:</Text>
                  <M3ESlider
                    value={sliderValue}
                    onValueChange={setSliderValue}
                    minimumValue={0}
                    maximumValue={100}
                    orientation="horizontal"
                    size="lg"
                    showValueIndicator={true}
                    className="flex-1"
                  />
                </View>
                <View className={classes.sliderRow}>
                  <Text className={classes.sliderLabel}>XL:</Text>
                  <M3ESlider
                    value={sliderValue}
                    onValueChange={setSliderValue}
                    minimumValue={0}
                    maximumValue={100}
                    orientation="horizontal"
                    size="xl"
                    showValueIndicator={true}
                    className="flex-1"
                  />
                </View>
              </View>

              <Text className={classes.subsectionTitle}>With Track Stops</Text>
              <View className={classes.sliderContainer}>
                <M3ESlider
                  value={sliderValue}
                  onValueChange={setSliderValue}
                  minimumValue={0}
                  maximumValue={100}
                  orientation="horizontal"
                  size="md"
                  showValueIndicator={true}
                  showStops={true}
                  stopCount={5}
                />
              </View>

              <Text className={classes.subsectionTitle}>Disabled State</Text>
              <View className={classes.sliderContainer}>
                <M3ESlider
                  value={75}
                  minimumValue={0}
                  maximumValue={100}
                  orientation="horizontal"
                  size="md"
                  disabled={true}
                  showValueIndicator={true}
                />
              </View>

              <Text className={classes.subsectionTitle}>
                Vertical Orientation
              </Text>
              <View className={classes.verticalSliderContainer}>
                <M3ESlider
                  value={sliderValue}
                  onValueChange={setSliderValue}
                  minimumValue={0}
                  maximumValue={100}
                  orientation="vertical"
                  size="md"
                  showValueIndicator={true}
                  className="h-48"
                />
              </View>
            </View>

            {/* M3E Switch */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Switch</Text>

              <Text className={classes.subsectionTitle}>Basic Switch</Text>
              <View className={classes.buttonRow}>
                <M3ESwitch
                  value={switchValue}
                  onValueChange={setSwitchValue}
                  showIcon={true}
                />
              </View>
            </View>

            {/* M3E Text Field */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Text Field</Text>

              <Text className={classes.subsectionTitle}>
                Outlined Text Field
              </Text>
              <View className={classes.buttonRow}>
                <M3ETextField
                  label="用户名"
                  placeholder="请输入用户名"
                  value={textFieldValue}
                  onChangeText={setTextFieldValue}
                  variant="outlined"
                  leadingIcon={
                    <Ionicons
                      name="person-outline"
                      size={20}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                />
              </View>
            </View>

            {/* M3E Tabs */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Tabs</Text>

              <Text className={classes.subsectionTitle}>Primary Tabs</Text>
              <View className={classes.buttonRow}>
                <M3ETabs
                  items={[
                    {
                      id: 'tab1',
                      label: '首页',
                      icon: <Ionicons name="home" size={20} />,
                    },
                    {
                      id: 'tab2',
                      label: '搜索',
                      icon: <Ionicons name="search" size={20} />,
                    },
                    {
                      id: 'tab3',
                      label: '个人',
                      icon: <Ionicons name="person" size={20} />,
                    },
                  ]}
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                  variant="primary"
                  configuration="label-and-icon"
                />
              </View>
            </View>

            {/* M3E Toolbar */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Toolbar</Text>

              <Text className={classes.subsectionTitle}>Floating Toolbar</Text>
              <View className={classes.buttonRow}>
                <M3EToolbar
                  actions={[
                    {
                      id: 'edit',
                      icon: <Ionicons name="create-outline" size={20} />,
                      onPress: () => console.log('Edit pressed'),
                    },
                    {
                      id: 'share',
                      icon: <Ionicons name="share-outline" size={20} />,
                      onPress: () => console.log('Share pressed'),
                    },
                    {
                      id: 'delete',
                      icon: <Ionicons name="trash-outline" size={20} />,
                      onPress: () => console.log('Delete pressed'),
                    },
                  ]}
                  configuration="floating"
                  orientation="horizontal"
                  type="standard"
                />
              </View>
            </View>

            {/* M3E Tooltip */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Tooltip</Text>

              <Text className={classes.subsectionTitle}>Plain Tooltip</Text>
              <View className={classes.buttonRow}>
                <M3ETooltip
                  content="这是一个工具提示"
                  visible={showTooltip}
                  onVisibleChange={setShowTooltip}
                >
                  <M3EButton
                    variant="outlined"
                    size="medium"
                    onPress={() => setShowTooltip(!showTooltip)}
                  >
                    显示提示
                  </M3EButton>
                </M3ETooltip>
              </View>
            </View>

            {/* M3E Snackbar */}
            <View className={classes.section}>
              <Text className={classes.sectionTitle}>M3E Snackbar</Text>

              <Text className={classes.subsectionTitle}>Basic Snackbar</Text>
              <View className={classes.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="medium"
                  onPress={() => setShowSnackbar(true)}
                >
                  显示 Snackbar
                </M3EButton>
              </View>

              <M3ESnackbar
                visible={showSnackbar}
                message="这是一个消息提示"
                actionText="确定"
                onActionPress={() => setShowSnackbar(false)}
                showCloseButton={true}
                onClose={() => setShowSnackbar(false)}
                duration={4000}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
