import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';



interface FeaturedStoryCardContentProps {
  summary: string;
  themeTags: string[];
}

export function FeaturedStoryCardContent({
  summary,
  themeTags,
}: FeaturedStoryCardContentProps) {
  return (
    <>
      <Text className="text-sm text-white mb-3 line-clamp-2">{summary}</Text>

      <View className="flex flex-row flex-wrap mb-4 gap-2">
        {themeTags.map((tag) => (
          <View key={tag} className="bg-white/20 px-2 py-1 rounded-full">
            <Text className="text-xs text-white">{tag}</Text>
          </View>
        ))}
      </View>
    </>
  );
}
