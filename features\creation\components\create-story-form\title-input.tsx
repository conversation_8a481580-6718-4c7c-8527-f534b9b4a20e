import React from 'react';
import { View } from 'react-native';

import { Text } from '@/components/ui/text';
import { M3ETextField, M3ETextFieldField } from '@/components/ui/m3e-text-field';

import { Bookmark } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface TitleInputProps {
  title: string;
  onTitleChange: (text: string) => void;
}

export default function TitleInput({ title, onTitleChange }: TitleInputProps) {
  const { t } = useTranslation();

  return (
    <View className="mb-6">
      <View className="flex flex-row items-center mb-2">
        <Bookmark size={18} className="text-primary-500" />
        <Text
          size="md"
          className="font-medium text-typography-900 dark:text-typography-50 ml-2"
        >
          {t('storyForm.titleLabel', '标题')}
        </Text>
      </View>
      <M3ETextField variant="outline"
        size="md"
        className={`rounded-md ${
          title.length > 0
            ? 'border-primary-500'
            : 'border-background-300 dark:border-background-700'
        }`}
      >
        <M3ETextField value={title}
          onChangeText={onTitleChange}
          placeholder={t('storyForm.titlePlaceholder', '输入故事标题')}
          maxLength={100}
         />
      </M3ETextField>
      {title.length > 0 && (
        <Text size="xs" className="text-typography-500 self-end mt-1">
          {title.length}/100
        </Text>
      )}
    </View>
  );
}
