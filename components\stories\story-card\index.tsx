import React from 'react';
import { Image , View } from 'react-native';
import { useRouter } from 'expo-router';
import { Story as ApiStory } from '@/api/stories';
import { PremiumBadge } from './premium-badge';
import { StoryStats } from './story-stats';

import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';

import { useWindowDimensions } from 'react-native';

export type { ApiStory as Story };

interface StoryCardProps {
  story: ApiStory;
  onPress?: (storyId: string) => void;
  style?: any; // Allow passing additional styles
  className?: string; // Allow passing additional className
}

export default function StoryCard({
  story,
  onPress,
  style,
  className = '',
}: StoryCardProps) {
  const router = useRouter();
  const { width } = useWindowDimensions();
  const cardWidth = (width - 16 * 3) / 2; // 16 = md spacing

  const coverImageUrl = story.cover_image_url;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const isPremium = false;

  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    } else {
      router.push(`/stories/${story.id}`);
    }
  };

  return (
    <Pressable
      onPress={handlePress}
      className={`rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden bg-background-50 dark:bg-background-900 shadow-sm ${className}`}
      style={[{ width: cardWidth }, style]}
    >
      <View className="relative">
        <Image
          source={
            coverImageUrl
              ? { uri: coverImageUrl }
              : require('../../../assets/images/default-story-placeholder.png')
          }
          style={{
            width: '100%',
            height: cardWidth * 0.75,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
        />

        <PremiumBadge visible={isPremium} />
      </View>

      <View className="flex flex-col p-2">
        <Text
          className="font-bold text-base mb-0.5 text-typography-900 dark:text-typography-50"
          numberOfLines={1}
        >
          {story.title}
        </Text>

        <Text
          className="text-xs text-typography-500 dark:text-typography-400"
          numberOfLines={1}
        >
          @{authorName}
        </Text>

        <StoryStats views={views} likes={likes} />
      </View>
    </Pressable>
  );
}
