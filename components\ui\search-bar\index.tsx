import React from 'react';
import { View } from 'react-native';

import { M3ETextField } from '@/components/ui/m3e-text-field';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { Search, X } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface SearchBarProps {
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  autoFocus?: boolean;
  placeholder?: string;
}

export default function SearchBar({
  value,
  onChangeText,
  onSearch,
  autoFocus = false,
  placeholder,
}: SearchBarProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [searchText, setSearchText] = React.useState(value || '');

  // 如果外部 value 变化，更新内部状态
  React.useEffect(() => {
    if (value !== undefined) {
      setSearchText(value);
    }
  }, [value]);

  const handleChangeText = (text: string) => {
    // 如果提供了外部 onChangeText，则调用它
    if (onChangeText) {
      onChangeText(text);
    } else {
      // 否则使用内部状态
      setSearchText(text);
    }
  };

  const handleSubmit = () => {
    const textToSearch = value !== undefined ? value : searchText;
    if (onSearch && textToSearch.trim()) {
      onSearch(textToSearch);
    }
  };

  const handleClear = () => {
    if (onChangeText) {
      onChangeText('');
    }
    setSearchText('');
  };

  return (
    <View className="flex flex-row" className={`h-11 rounded-lg border items-center ${
        isDark
          ? 'border-outline-700 bg-background-900'
          : 'border-outline-200 bg-background-50'
      }`}>
      <View className="pl-3">
        <Search
          className={isDark ? 'text-typography-500' : 'text-typography-400'}
          size={16}
        />
      </View>

      <M3ETextField
        className="flex-1 h-full"
        variant="filled"
        placeholder={
          placeholder || t('searchPlaceholder', '搜索故事、作者、标签...')
        }
        value={value !== undefined ? value : searchText}
        onChangeText={handleChangeText}
        onSubmitEditing={handleSubmit}
        returnKeyType="search"
        autoFocus={autoFocus}
      />

      {(value || searchText) && (value || searchText).length > 0 && (
        <View className="pr-3">
          <Button
            variant="text"
            size="small"
            onPress={handleClear}
            className="p-1"
          >
            <ButtonIcon>
              <X size={16} />
            </ButtonIcon>
          </Button>
        </View>
      )}
    </View>
  );
}
